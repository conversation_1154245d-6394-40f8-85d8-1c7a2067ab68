"""
测试循环导入修复
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("测试循环导入修复...")

try:
    print("1. 测试导入 StockData...")
    from src.models.stock import StockData
    print("OK - StockData 导入成功")
    
    print("2. 测试导入 StockDataLoader...")
    from src.models.data_loader import StockDataLoader
    print("OK - StockDataLoader 导入成功")
    
    print("3. 测试导入 StockService...")
    from src.services.stock_service import StockService
    print("OK - StockService 导入成功")
    
    print("4. 测试导入 MainWindow...")
    from src.ui.main_window import MainWindow
    print("OK - MainWindow 导入成功")
    
    print("5. 测试创建 StockDataLoader 实例...")
    loader = StockDataLoader()
    print("OK - StockDataLoader 实例创建成功")
    
    print("6. 测试创建 StockService 实例...")
    service = StockService()
    print("OK - StockService 实例创建成功")
    
    print("所有测试通过！循环导入问题已修复。")
    
except Exception as e:
    print(f"ERROR - 测试失败: {e}")
    import traceback
    traceback.print_exc()
