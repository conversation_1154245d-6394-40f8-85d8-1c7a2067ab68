"""
数据获取功能使用示例
展示如何在实际项目中使用增强的数据获取功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_providers.unified_data_manager import UnifiedDataManager


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建数据管理器
    manager = UnifiedDataManager()
    
    # 1. 获取股票列表
    print("1. 获取股票列表...")
    stock_list = manager.get_stock_list()
    if stock_list is not None:
        print(f"获取到 {len(stock_list)} 只股票")
        # 选择一只股票进行后续操作
        symbol = stock_list['symbol'].iloc[0]
        print(f"选择股票: {symbol}")
        
        # 2. 获取历史数据
        print(f"\n2. 获取 {symbol} 的历史数据...")
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
        
        daily_data = manager.get_daily_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date
        )
        
        if daily_data is not None:
            print(f"获取到 {len(daily_data)} 条历史数据")
            print(f"最新收盘价: {daily_data['close'].iloc[-1]:.2f}")
        
        # 3. 获取实时数据
        print(f"\n3. 获取 {symbol} 的实时数据...")
        realtime_data = manager.get_realtime_data([symbol])
        
        if realtime_data is not None:
            print(f"实时价格: {realtime_data['price'].iloc[0]:.2f}")
            if 'change_pct' in realtime_data.columns:
                print(f"涨跌幅: {realtime_data['change_pct'].iloc[0]:.2f}%")


def example_batch_operations():
    """批量操作示例"""
    print("\n=== 批量操作示例 ===")
    
    manager = UnifiedDataManager()
    
    # 定义要分析的股票列表
    symbols = ['000001', '000002', '600000', '600036', '000858']
    
    print(f"批量获取 {len(symbols)} 只股票的数据...")
    
    # 批量获取历史数据
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    batch_data = manager.batch_get_daily_data(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        max_workers=3
    )
    
    print(f"成功获取 {len(batch_data)} 只股票的数据")
    
    # 分析数据
    for symbol, data in batch_data.items():
        if data is not None and not data.empty:
            current_price = data['close'].iloc[-1]
            price_change = ((current_price - data['close'].iloc[0]) / data['close'].iloc[0]) * 100
            print(f"{symbol}: 当前价格 {current_price:.2f}, 30天涨跌幅 {price_change:.2f}%")


def example_technical_analysis():
    """技术分析示例"""
    print("\n=== 技术分析示例 ===")
    
    manager = UnifiedDataManager()
    
    symbol = '000001'  # 平安银行
    
    print(f"获取 {symbol} 的技术指标...")
    
    # 获取带技术指标的数据
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=100)).strftime("%Y%m%d")
    
    tech_data = manager.get_technical_indicators(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )
    
    if tech_data is not None and not tech_data.empty:
        latest = tech_data.iloc[-1]
        
        print(f"最新技术指标:")
        print(f"  收盘价: {latest['close']:.2f}")
        
        if 'ma5' in tech_data.columns:
            print(f"  5日均线: {latest['ma5']:.2f}")
        if 'ma10' in tech_data.columns:
            print(f"  10日均线: {latest['ma10']:.2f}")
        if 'ma20' in tech_data.columns:
            print(f"  20日均线: {latest['ma20']:.2f}")
        
        if 'rsi' in tech_data.columns:
            print(f"  RSI: {latest['rsi']:.2f}")
        
        if 'macd_dif' in tech_data.columns:
            print(f"  MACD DIF: {latest['macd_dif']:.4f}")


def example_market_monitoring():
    """市场监控示例"""
    print("\n=== 市场监控示例 ===")
    
    manager = UnifiedDataManager()
    
    # 1. 获取市场概况
    print("1. 获取市场概况...")
    market_data = manager.get_market_summary()
    
    if market_data is not None:
        print("市场概况获取成功")
    
    # 2. 获取热门股票
    print("\n2. 获取热门股票...")
    stock_list = manager.get_stock_list()
    
    if stock_list is not None and 'change_pct' in stock_list.columns:
        # 涨幅榜前10
        top_gainers = stock_list.nlargest(10, 'change_pct')
        print("涨幅榜前10:")
        for _, row in top_gainers.iterrows():
            print(f"  {row['symbol']} {row['name']}: {row['change_pct']:.2f}%")
    
    # 3. 获取板块数据
    print("\n3. 获取板块数据...")
    sector_data = manager.get_sector_data()
    
    if sector_data is not None:
        print(f"获取到 {len(sector_data)} 个板块数据")


def example_performance_monitoring():
    """性能监控示例"""
    print("\n=== 性能监控示例 ===")
    
    manager = UnifiedDataManager()
    
    # 执行一些操作
    stock_list = manager.get_stock_list()
    
    if stock_list is not None:
        symbol = stock_list['symbol'].iloc[0]
        daily_data = manager.get_daily_data(symbol)
        realtime_data = manager.get_realtime_data([symbol])
    
    # 获取统计信息
    stats = manager.get_stats()
    
    print("系统统计信息:")
    manager_stats = stats['manager_stats']
    print(f"  总请求数: {manager_stats['total_requests']}")
    print(f"  成功请求: {manager_stats['successful_requests']}")
    print(f"  失败请求: {manager_stats['failed_requests']}")
    
    if 'akshare' in stats['provider_stats']:
        provider_stats = stats['provider_stats']['akshare']
        print(f"  缓存命中率: {provider_stats['cache_hit_rate']:.1%}")
        print(f"  内存缓存大小: {provider_stats['memory_cache_size']}")
        print(f"  文件缓存大小: {provider_stats['file_cache_size']}")
    
    # 健康检查
    health = manager.health_check()
    print(f"\n系统健康状态: {health['status']}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    manager = UnifiedDataManager()
    
    # 尝试获取不存在的股票数据
    print("尝试获取不存在的股票数据...")
    invalid_data = manager.get_daily_data('INVALID_SYMBOL')
    
    if invalid_data is None:
        print("正确处理了无效股票代码")
    
    # 尝试获取无效日期范围的数据
    print("尝试获取无效日期范围的数据...")
    invalid_date_data = manager.get_daily_data(
        symbol='000001',
        start_date='20300101',  # 未来日期
        end_date='20300131'
    )
    
    if invalid_date_data is None or invalid_date_data.empty:
        print("正确处理了无效日期范围")


def main():
    """主函数"""
    print("数据获取功能使用示例")
    print("="*50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_batch_operations()
        example_technical_analysis()
        example_market_monitoring()
        example_performance_monitoring()
        example_error_handling()
        
        print("\n" + "="*50)
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")


if __name__ == "__main__":
    main()
