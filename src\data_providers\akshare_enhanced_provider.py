"""
基于AKShare的增强数据提供者
实现了完整的A股数据获取功能
"""

import logging
import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta
import akshare as ak
import warnings

from .enhanced_data_provider import EnhancedDataProvider, DataSource, DataRequest

# 忽略警告
warnings.filterwarnings('ignore')


class AKShareEnhancedProvider(EnhancedDataProvider):
    """基于AKShare的增强数据提供者"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # AKShare特定配置
        self.supported_data_types = {
            'stock_list': self._get_stock_list,
            'daily_data': self._get_daily_data,
            'realtime_data': self._get_realtime_data,
            'minute_data': self._get_minute_data,
            'financial_data': self._get_financial_data,
            'technical_indicators': self._get_technical_indicators,
            'market_summary': self._get_market_summary,
            'sector_data': self._get_sector_data,
            'news_data': self._get_news_data,
            'dividend_data': self._get_dividend_data
        }
        
        self.logger.info("AKShare增强数据提供者初始化完成")
    
    def _fetch_data_from_source(self, request: DataRequest, source: DataSource) -> Optional[pd.DataFrame]:
        """从指定数据源获取数据"""
        if source != DataSource.AKSHARE:
            return None
        
        data_type = request.data_type
        if data_type not in self.supported_data_types:
            self.logger.error(f"不支持的数据类型: {data_type}")
            return None
        
        try:
            return self.supported_data_types[data_type](request)
        except Exception as e:
            self.logger.error(f"获取数据失败: {e}")
            return None
    
    def _get_stock_list(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取股票列表"""
        try:
            # 获取A股列表
            stock_list = ak.stock_zh_a_spot_em()
            
            if stock_list is not None and not stock_list.empty:
                # 标准化列名
                column_mapping = {
                    '代码': 'symbol',
                    '名称': 'name',
                    '最新价': 'price',
                    '涨跌幅': 'change_pct',
                    '涨跌额': 'change',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '总市值': 'market_cap',
                    '流通市值': 'float_market_cap'
                }
                
                stock_list = stock_list.rename(columns=column_mapping)
                
                # 添加市场标识
                stock_list['market'] = stock_list['symbol'].apply(self._get_market_from_symbol)
                
                # 数据类型转换
                numeric_columns = ['price', 'change_pct', 'change', 'volume', 'amount', 'market_cap', 'float_market_cap']
                for col in numeric_columns:
                    if col in stock_list.columns:
                        stock_list[col] = pd.to_numeric(stock_list[col], errors='coerce')
                
                self.logger.info(f"成功获取股票列表，共 {len(stock_list)} 只股票")
                return stock_list
            
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {e}")
        
        return None
    
    def _get_daily_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取日线数据"""
        try:
            symbol = request.symbol
            start_date = request.start_date
            end_date = request.end_date

            # 尝试不同的参数组合
            try:
                # 方法1：只使用symbol参数
                if start_date and end_date:
                    data = ak.stock_zh_a_hist(symbol=symbol, period="daily",
                                            start_date=start_date, end_date=end_date, adjust="qfq")
                else:
                    data = ak.stock_zh_a_hist(symbol=symbol, period="daily", adjust="qfq")

            except Exception as e1:
                self.logger.warning(f"方法1失败: {e1}")
                try:
                    # 方法2：使用默认参数
                    data = ak.stock_zh_a_hist(symbol=symbol)
                except Exception as e2:
                    self.logger.warning(f"方法2失败: {e2}")
                    # 方法3：尝试其他接口
                    data = None

            if data is not None and not data.empty:
                # 标准化列名
                column_mapping = {
                    '日期': 'date',
                    '开盘': 'open',
                    '收盘': 'close',
                    '最高': 'high',
                    '最低': 'low',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '振幅': 'amplitude',
                    '涨跌幅': 'change_pct',
                    '涨跌额': 'change',
                    '换手率': 'turnover'
                }

                # 只重命名存在的列
                existing_columns = {k: v for k, v in column_mapping.items() if k in data.columns}
                data = data.rename(columns=existing_columns)

                # 确保日期格式正确
                if 'date' in data.columns:
                    data['date'] = pd.to_datetime(data['date'])
                    data = data.sort_values('date')
                elif '日期' in data.columns:
                    data['date'] = pd.to_datetime(data['日期'])
                    data = data.sort_values('date')

                # 数据类型转换
                numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount',
                                 'amplitude', 'change_pct', 'change', 'turnover']
                for col in numeric_columns:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')

                # 添加基本技术指标
                if len(data) > 0:
                    data = self._add_basic_indicators(data)

                self.logger.info(f"成功获取 {symbol} 日线数据，共 {len(data)} 条记录")
                return data

        except Exception as e:
            self.logger.error(f"获取日线数据失败: {e}")

        return None
    
    def _get_realtime_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取实时数据"""
        try:
            symbol = request.symbol

            # 尝试不同的实时数据接口
            try:
                # 方法1：使用bid_ask接口
                realtime_data = ak.stock_bid_ask_em(symbol=symbol)

                if realtime_data is not None and not realtime_data.empty:
                    # 转换为标准格式
                    data_dict = {}
                    for _, row in realtime_data.iterrows():
                        data_dict[row['item']] = row['value']

                    # 创建DataFrame
                    data = pd.DataFrame([{
                        'symbol': symbol,
                        'datetime': datetime.now(),
                        'price': data_dict.get('最新', 0),
                        'open': data_dict.get('今开', 0),
                        'high': data_dict.get('最高', 0),
                        'low': data_dict.get('最低', 0),
                        'pre_close': data_dict.get('昨收', 0),
                        'change': data_dict.get('涨跌', 0),
                        'change_pct': data_dict.get('涨幅', 0),
                        'volume': data_dict.get('总手', 0),
                        'amount': data_dict.get('金额', 0),
                        'turnover': data_dict.get('换手', 0),
                        'buy1_price': data_dict.get('buy_1', 0),
                        'buy1_volume': data_dict.get('buy_1_vol', 0),
                        'sell1_price': data_dict.get('sell_1', 0),
                        'sell1_volume': data_dict.get('sell_1_vol', 0)
                    }])

                    self.logger.info(f"成功获取 {symbol} 实时数据")
                    return data

            except Exception as e1:
                self.logger.warning(f"bid_ask接口失败: {e1}")

                try:
                    # 方法2：从股票列表中获取实时价格
                    stock_list = ak.stock_zh_a_spot_em()
                    if stock_list is not None and not stock_list.empty:
                        # 查找对应股票
                        stock_info = stock_list[stock_list['代码'] == symbol]
                        if not stock_info.empty:
                            row = stock_info.iloc[0]
                            data = pd.DataFrame([{
                                'symbol': symbol,
                                'datetime': datetime.now(),
                                'price': row.get('最新价', 0),
                                'change_pct': row.get('涨跌幅', 0),
                                'change': row.get('涨跌额', 0),
                                'volume': row.get('成交量', 0),
                                'amount': row.get('成交额', 0),
                                'turnover': row.get('换手率', 0)
                            }])

                            self.logger.info(f"成功从股票列表获取 {symbol} 实时数据")
                            return data

                except Exception as e2:
                    self.logger.warning(f"股票列表方法失败: {e2}")

        except Exception as e:
            self.logger.error(f"获取实时数据失败: {e}")

        return None
    
    def _get_minute_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取分钟数据"""
        try:
            symbol = request.symbol
            period = request.period or "1"  # 默认1分钟
            
            # AKShare分钟数据接口
            data = ak.stock_zh_a_hist_min_em(symbol=symbol, period=period, adjust="qfq")
            
            if data is not None and not data.empty:
                # 标准化列名
                column_mapping = {
                    '时间': 'datetime',
                    '开盘': 'open',
                    '收盘': 'close',
                    '最高': 'high',
                    '最低': 'low',
                    '成交量': 'volume',
                    '成交额': 'amount'
                }
                
                data = data.rename(columns=column_mapping)
                
                # 确保时间格式正确
                if 'datetime' in data.columns:
                    data['datetime'] = pd.to_datetime(data['datetime'])
                    data = data.sort_values('datetime')
                
                # 数据类型转换
                numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount']
                for col in numeric_columns:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')
                
                self.logger.info(f"成功获取 {symbol} {period}分钟数据，共 {len(data)} 条记录")
                return data
            
        except Exception as e:
            self.logger.error(f"获取分钟数据失败: {e}")
        
        return None
    
    def _get_financial_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取财务数据"""
        try:
            symbol = request.symbol
            
            # 获取财务指标
            financial_data = ak.stock_financial_em(symbol=symbol)
            
            if financial_data is not None and not financial_data.empty:
                # 标准化列名（根据实际返回的列名进行调整）
                self.logger.info(f"成功获取 {symbol} 财务数据")
                return financial_data
            
        except Exception as e:
            self.logger.error(f"获取财务数据失败: {e}")
        
        return None
    
    def _get_technical_indicators(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取技术指标数据"""
        # 首先获取基础价格数据
        price_request = DataRequest(
            symbol=request.symbol,
            data_type='daily_data',
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        price_data = self._get_daily_data(price_request)
        if price_data is None or price_data.empty:
            return None
        
        # 计算技术指标
        return self._calculate_technical_indicators(price_data)
    
    def _get_market_summary(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取市场概况"""
        try:
            # 获取上证概况
            sse_summary = ak.stock_sse_summary()
            
            # 获取深证概况
            today = datetime.now().strftime("%Y%m%d")
            szse_summary = ak.stock_szse_summary(date=today)
            
            # 合并数据
            market_data = {
                'date': today,
                'sse_data': sse_summary.to_dict() if sse_summary is not None else {},
                'szse_data': szse_summary.to_dict() if szse_summary is not None else {}
            }
            
            data = pd.DataFrame([market_data])
            self.logger.info("成功获取市场概况数据")
            return data
            
        except Exception as e:
            self.logger.error(f"获取市场概况失败: {e}")
        
        return None
    
    def _get_sector_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取板块数据"""
        try:
            # 获取概念板块数据
            concept_data = ak.stock_board_concept_name_em()
            
            if concept_data is not None and not concept_data.empty:
                self.logger.info(f"成功获取板块数据，共 {len(concept_data)} 个板块")
                return concept_data
            
        except Exception as e:
            self.logger.error(f"获取板块数据失败: {e}")
        
        return None
    
    def _get_news_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取新闻数据"""
        try:
            symbol = request.symbol
            
            # 获取个股新闻
            news_data = ak.stock_news_em(symbol=symbol)
            
            if news_data is not None and not news_data.empty:
                self.logger.info(f"成功获取 {symbol} 新闻数据，共 {len(news_data)} 条")
                return news_data
            
        except Exception as e:
            self.logger.error(f"获取新闻数据失败: {e}")
        
        return None
    
    def _get_dividend_data(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """获取分红数据"""
        try:
            symbol = request.symbol
            
            # 获取分红配送数据
            dividend_data = ak.stock_fhps_em(symbol=symbol)
            
            if dividend_data is not None and not dividend_data.empty:
                self.logger.info(f"成功获取 {symbol} 分红数据")
                return dividend_data
            
        except Exception as e:
            self.logger.error(f"获取分红数据失败: {e}")
        
        return None
    
    def _get_market_from_symbol(self, symbol: str) -> str:
        """根据股票代码判断市场"""
        if symbol.startswith('00') or symbol.startswith('30'):
            return 'SZSE'  # 深交所
        elif symbol.startswith('60') or symbol.startswith('68'):
            return 'SSE'   # 上交所
        elif symbol.startswith('8') or symbol.startswith('4'):
            return 'BSE'   # 北交所
        else:
            return 'UNKNOWN'
    
    def _add_basic_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加基本技术指标"""
        if 'close' not in data.columns or len(data) < 20:
            return data
        
        try:
            # 移动平均线
            data['ma5'] = data['close'].rolling(window=5).mean()
            data['ma10'] = data['close'].rolling(window=10).mean()
            data['ma20'] = data['close'].rolling(window=20).mean()
            
            # 成交量移动平均
            if 'volume' in data.columns:
                data['vol_ma5'] = data['volume'].rolling(window=5).mean()
                data['vol_ma10'] = data['volume'].rolling(window=10).mean()
            
        except Exception as e:
            self.logger.warning(f"计算基本指标失败: {e}")
        
        return data
    
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        if 'close' not in data.columns or len(data) < 26:
            return data
        
        try:
            # MACD
            exp1 = data['close'].ewm(span=12).mean()
            exp2 = data['close'].ewm(span=26).mean()
            data['macd_dif'] = exp1 - exp2
            data['macd_dea'] = data['macd_dif'].ewm(span=9).mean()
            data['macd_histogram'] = (data['macd_dif'] - data['macd_dea']) * 2
            
            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带
            data['bb_middle'] = data['close'].rolling(window=20).mean()
            bb_std = data['close'].rolling(window=20).std()
            data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
            data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
            
        except Exception as e:
            self.logger.warning(f"计算技术指标失败: {e}")
        
        return data
