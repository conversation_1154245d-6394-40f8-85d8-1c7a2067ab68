"""
测试AKShare基本功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import akshare as ak
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_akshare_version():
    """测试AKShare版本"""
    try:
        version = ak.__version__
        logger.info(f"AKShare版本: {version}")
        return True
    except Exception as e:
        logger.error(f"获取AKShare版本失败: {e}")
        return False


def test_stock_list():
    """测试股票列表获取"""
    logger.info("=== 测试股票列表获取 ===")
    
    try:
        # 获取A股列表
        stock_list = ak.stock_zh_a_spot_em()
        
        if stock_list is not None and not stock_list.empty:
            logger.info(f"成功获取股票列表，共 {len(stock_list)} 只股票")
            logger.info(f"列名: {list(stock_list.columns)}")
            logger.info(f"前3只股票:\n{stock_list.head(3)}")
            return True
        else:
            logger.error("获取股票列表失败")
            return False
            
    except Exception as e:
        logger.error(f"测试股票列表获取异常: {e}")
        return False


def test_daily_data_simple():
    """测试简单的日线数据获取"""
    logger.info("=== 测试简单日线数据获取 ===")
    
    try:
        # 测试不同的接口
        test_symbols = ['000001', '600000']
        
        for symbol in test_symbols:
            logger.info(f"测试获取 {symbol} 的日线数据...")
            
            try:
                # 使用最新的AKShare接口
                data = ak.stock_zh_a_hist(symbol=symbol, period="daily", adjust="qfq")
                
                if data is not None and not data.empty:
                    logger.info(f"成功获取 {symbol} 日线数据，共 {len(data)} 条记录")
                    logger.info(f"列名: {list(data.columns)}")
                    logger.info(f"最新数据:\n{data.tail(1)}")
                    return True
                else:
                    logger.warning(f"获取 {symbol} 日线数据为空")
                    
            except Exception as e:
                logger.error(f"获取 {symbol} 日线数据异常: {e}")
                continue
        
        return False
        
    except Exception as e:
        logger.error(f"测试日线数据获取异常: {e}")
        return False


def test_daily_data_with_dates():
    """测试带日期范围的日线数据获取"""
    logger.info("=== 测试带日期范围的日线数据获取 ===")
    
    try:
        symbol = '000001'
        start_date = '20241201'
        end_date = '20241212'
        
        logger.info(f"获取 {symbol} 从 {start_date} 到 {end_date} 的数据...")
        
        data = ak.stock_zh_a_hist(
            symbol=symbol, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            adjust="qfq"
        )
        
        if data is not None and not data.empty:
            logger.info(f"成功获取 {symbol} 日线数据，共 {len(data)} 条记录")
            logger.info(f"列名: {list(data.columns)}")
            logger.info(f"数据:\n{data}")
            return True
        else:
            logger.warning(f"获取 {symbol} 日线数据为空")
            return False
            
    except Exception as e:
        logger.error(f"测试带日期范围的日线数据获取异常: {e}")
        return False


def test_realtime_data():
    """测试实时数据获取"""
    logger.info("=== 测试实时数据获取 ===")
    
    try:
        symbol = '000001'
        
        # 测试实时行情
        data = ak.stock_bid_ask_em(symbol=symbol)
        
        if data is not None and not data.empty:
            logger.info(f"成功获取 {symbol} 实时数据")
            logger.info(f"列名: {list(data.columns)}")
            logger.info(f"实时数据:\n{data}")
            return True
        else:
            logger.warning(f"获取 {symbol} 实时数据为空")
            return False
            
    except Exception as e:
        logger.error(f"测试实时数据获取异常: {e}")
        return False


def test_market_summary():
    """测试市场概况获取"""
    logger.info("=== 测试市场概况获取 ===")
    
    try:
        # 获取上证概况
        sse_data = ak.stock_sse_summary()
        
        if sse_data is not None and not sse_data.empty:
            logger.info("成功获取上证概况")
            logger.info(f"上证概况:\n{sse_data}")
            return True
        else:
            logger.warning("获取上证概况为空")
            return False
            
    except Exception as e:
        logger.error(f"测试市场概况获取异常: {e}")
        return False


def test_different_interfaces():
    """测试不同的AKShare接口"""
    logger.info("=== 测试不同的AKShare接口 ===")
    
    interfaces = [
        ("股票列表", lambda: ak.stock_zh_a_spot_em()),
        ("上证概况", lambda: ak.stock_sse_summary()),
        ("概念板块", lambda: ak.stock_board_concept_name_em()),
    ]
    
    success_count = 0
    
    for name, func in interfaces:
        try:
            logger.info(f"测试 {name}...")
            data = func()
            
            if data is not None and not data.empty:
                logger.info(f"✅ {name} 成功，数据量: {len(data)}")
                success_count += 1
            else:
                logger.warning(f"⚠️ {name} 返回空数据")
                
        except Exception as e:
            logger.error(f"❌ {name} 失败: {e}")
    
    logger.info(f"接口测试完成，成功率: {success_count}/{len(interfaces)}")
    return success_count > 0


def main():
    """主测试函数"""
    logger.info("开始测试AKShare基本功能")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("AKShare版本", test_akshare_version),
        ("股票列表获取", test_stock_list),
        ("简单日线数据", test_daily_data_simple),
        ("带日期日线数据", test_daily_data_with_dates),
        ("实时数据获取", test_realtime_data),
        ("市场概况获取", test_market_summary),
        ("不同接口测试", test_different_interfaces)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        
        try:
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
    else:
        logger.warning(f"⚠️  有 {total - passed} 项测试失败")


if __name__ == "__main__":
    main()
