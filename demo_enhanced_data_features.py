"""
增强数据获取功能演示
展示完善后的数据获取系统的各种功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_providers.unified_data_manager import UnifiedDataManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def demo_stock_list():
    """演示股票列表获取功能"""
    print("\n" + "="*60)
    print("股票列表获取功能演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    # 获取全部股票列表
    stock_list = manager.get_stock_list()
    
    if stock_list is not None and not stock_list.empty:
        print(f"✅ 成功获取A股列表，共 {len(stock_list)} 只股票")
        print(f"📋 数据列: {list(stock_list.columns)}")
        
        # 按市场分类统计
        market_stats = stock_list['market'].value_counts()
        print(f"\n📈 市场分布:")
        for market, count in market_stats.items():
            print(f"  {market}: {count} 只")
        
        # 显示涨幅前5名
        if 'change_pct' in stock_list.columns:
            top_gainers = stock_list.nlargest(5, 'change_pct')[['symbol', 'name', 'price', 'change_pct']]
            print(f"\n🚀 今日涨幅前5名:")
            print(top_gainers.to_string(index=False))
        
        return stock_list['symbol'].head(10).tolist()
    
    return []


def demo_daily_data(symbols):
    """演示日线数据获取功能"""
    print("\n" + "="*60)
    print("📈 日线数据获取功能演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    # 设置日期范围
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
    
    # 获取单只股票的日线数据
    symbol = symbols[0] if symbols else "000001"
    print(f"📊 获取 {symbol} 最近30天的日线数据...")
    
    daily_data = manager.get_daily_data(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )
    
    if daily_data is not None and not daily_data.empty:
        print(f"✅ 成功获取 {len(daily_data)} 条日线数据")
        print(f"📋 数据列: {list(daily_data.columns)}")
        
        # 显示最新5天数据
        print(f"\n📅 最新5天数据:")
        latest_data = daily_data.tail(5)[['date', 'open', 'close', 'high', 'low', 'volume']]
        print(latest_data.to_string(index=False))
        
        # 计算一些统计信息
        if 'close' in daily_data.columns:
            current_price = daily_data['close'].iloc[-1]
            max_price = daily_data['close'].max()
            min_price = daily_data['close'].min()
            avg_volume = daily_data['volume'].mean() if 'volume' in daily_data.columns else 0
            
            print(f"\n📊 统计信息:")
            print(f"  当前价格: {current_price:.2f}")
            print(f"  30天最高: {max_price:.2f}")
            print(f"  30天最低: {min_price:.2f}")
            print(f"  平均成交量: {avg_volume:,.0f}")
    
    # 批量获取多只股票数据
    print(f"\n🔄 批量获取前3只股票的数据...")
    batch_symbols = symbols[:3]
    batch_data = manager.batch_get_daily_data(
        symbols=batch_symbols,
        start_date=start_date,
        end_date=end_date,
        max_workers=3
    )
    
    if batch_data:
        print(f"✅ 批量获取成功，获得 {len(batch_data)} 只股票的数据")
        for sym, data in batch_data.items():
            print(f"  {sym}: {len(data)} 条记录")


def demo_realtime_data(symbols):
    """演示实时数据获取功能"""
    print("\n" + "="*60)
    print("⚡ 实时数据获取功能演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    # 获取多只股票的实时数据
    test_symbols = symbols[:5]
    print(f"📡 获取 {len(test_symbols)} 只股票的实时数据...")
    
    realtime_data = manager.get_realtime_data(test_symbols)
    
    if realtime_data is not None and not realtime_data.empty:
        print(f"✅ 成功获取 {len(realtime_data)} 条实时数据")
        
        # 显示实时数据
        display_columns = ['symbol', 'price', 'change_pct', 'volume', 'amount']
        available_columns = [col for col in display_columns if col in realtime_data.columns]
        
        print(f"\n📊 实时行情:")
        print(realtime_data[available_columns].to_string(index=False))


def demo_technical_indicators(symbols):
    """演示技术指标计算功能"""
    print("\n" + "="*60)
    print("📊 技术指标计算功能演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    symbol = symbols[0] if symbols else "000001"
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=100)).strftime("%Y%m%d")
    
    print(f"🔍 计算 {symbol} 的技术指标...")
    
    tech_data = manager.get_technical_indicators(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date
    )
    
    if tech_data is not None and not tech_data.empty:
        print(f"✅ 成功计算技术指标，共 {len(tech_data)} 条记录")
        
        # 查找技术指标列
        indicator_columns = [col for col in tech_data.columns 
                           if any(indicator in col.lower() 
                                for indicator in ['ma', 'macd', 'rsi', 'bb'])]
        
        if indicator_columns:
            print(f"📈 技术指标列: {indicator_columns}")
            
            # 显示最新的技术指标值
            latest_indicators = tech_data[['date'] + indicator_columns].tail(1)
            print(f"\n📊 最新技术指标:")
            for col in indicator_columns:
                if col in latest_indicators.columns:
                    value = latest_indicators[col].iloc[0]
                    if pd.notna(value):
                        print(f"  {col}: {value:.4f}")


def demo_market_summary():
    """演示市场概况功能"""
    print("\n" + "="*60)
    print("🏛️ 市场概况功能演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    print("📊 获取市场概况数据...")
    market_data = manager.get_market_summary()
    
    if market_data is not None and not market_data.empty:
        print("✅ 成功获取市场概况")
        print(f"📋 数据列: {list(market_data.columns)}")
        
        # 显示市场概况信息
        if 'sse_data' in market_data.columns:
            sse_data = market_data['sse_data'].iloc[0]
            if isinstance(sse_data, dict) and sse_data:
                print(f"\n🏛️ 上海证券交易所概况:")
                for key, value in sse_data.items():
                    print(f"  {key}: {value}")


def demo_cache_performance():
    """演示缓存性能"""
    print("\n" + "="*60)
    print("⚡ 缓存性能演示")
    print("="*60)
    
    manager = UnifiedDataManager(cache_ttl=300)  # 5分钟缓存
    
    # 第一次获取数据
    print("🔄 第一次获取股票列表（无缓存）...")
    start_time = datetime.now()
    data1 = manager.get_stock_list()
    first_duration = (datetime.now() - start_time).total_seconds()
    
    # 第二次获取数据
    print("🔄 第二次获取股票列表（使用缓存）...")
    start_time = datetime.now()
    data2 = manager.get_stock_list()
    second_duration = (datetime.now() - start_time).total_seconds()
    
    print(f"\n⏱️ 性能对比:")
    print(f"  第一次耗时: {first_duration:.3f}秒")
    print(f"  第二次耗时: {second_duration:.3f}秒")
    print(f"  性能提升: {(first_duration/second_duration):.1f}倍")
    
    # 显示统计信息
    stats = manager.get_stats()
    provider_stats = stats['provider_stats']['akshare']
    
    print(f"\n📊 缓存统计:")
    print(f"  缓存命中率: {provider_stats['cache_hit_rate']:.1%}")
    print(f"  内存缓存大小: {provider_stats['memory_cache_size']}")
    print(f"  文件缓存大小: {provider_stats['file_cache_size']}")


def demo_health_check():
    """演示健康检查功能"""
    print("\n" + "="*60)
    print("🏥 系统健康检查演示")
    print("="*60)
    
    manager = UnifiedDataManager()
    
    print("🔍 执行系统健康检查...")
    health = manager.health_check()
    
    print(f"✅ 系统状态: {health['status']}")
    print(f"🕐 检查时间: {health['timestamp']}")
    
    print(f"\n📊 数据源状态:")
    for source, status in health['providers'].items():
        status_icon = "✅" if status['status'] == 'healthy' else "❌"
        print(f"  {status_icon} {source}: {status['status']}")
        if 'data_count' in status:
            print(f"    数据量: {status['data_count']}")
        if 'error' in status:
            print(f"    错误: {status['error']}")


def main():
    """主演示函数"""
    print("智能股票分析系统 - 增强数据获取功能演示")
    print("="*80)
    
    try:
        # 1. 股票列表获取
        symbols = demo_stock_list()
        
        if symbols:
            # 2. 日线数据获取
            demo_daily_data(symbols)
            
            # 3. 实时数据获取
            demo_realtime_data(symbols)
            
            # 4. 技术指标计算
            demo_technical_indicators(symbols)
        
        # 5. 市场概况
        demo_market_summary()
        
        # 6. 缓存性能
        demo_cache_performance()
        
        # 7. 健康检查
        demo_health_check()
        
        print("\n" + "="*80)
        print("数据获取功能演示完成！")
        print("所有功能都正常工作")
        print("系统已具备完整的股票数据获取能力")
        print("="*80)
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"\n演示失败: {e}")


if __name__ == "__main__":
    import pandas as pd
    main()
