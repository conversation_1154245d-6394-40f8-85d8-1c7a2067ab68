"""股票数据加载器,支持多数据源获取数据"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
import logging
import requests
import json
import akshare as ak
from ..utils.akshare_data_provider import AKShareDataProvider
import time

class StockDataLoader:
    """股票数据加载器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.akshare_provider = AKShareDataProvider()
        self.data_sources = [
            self._get_data_from_akshare
        ]
        # 设置重试次数和等待时间
        self.max_retries = 3
        self.retry_wait = 2  # 秒
    
    def _retry_wrapper(self, func, *args, **kwargs):
        """包装函数，添加重试逻辑和数据缓存"""
        cache_key = f"{func.__name__}_{args}_{kwargs}"
        
        # 尝试从缓存获取数据
        if hasattr(self, '_data_cache') and cache_key in self._data_cache:
            self.logger.info(f"从缓存获取数据: {cache_key}")
            return self._data_cache[cache_key]
        
        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                
                # 缓存成功获取的数据
                if not hasattr(self, '_data_cache'):
                    self._data_cache = {}
                self._data_cache[cache_key] = result
                
                return result
            except Exception as e:
                self.logger.warning(f"尝试 {attempt+1}/{self.max_retries} 失败: {str(e)}")
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_wait * (attempt + 1)  # 指数退避
                    self.logger.info(f"等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"所有尝试都失败: {str(e)}")
                    raise
    
    def calculate_ma(self, df: pd.DataFrame, windows: List[int] = [5, 10, 20, 30, 60]) -> pd.DataFrame:
        """计算移动平均线"""
        for window in windows:
            df[f'MA{window}'] = df['收盘'].rolling(window=window).mean()
        return df
        
    def calculate_macd(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        ema12 = df['收盘'].ewm(span=12, adjust=False).mean()
        ema26 = df['收盘'].ewm(span=26, adjust=False).mean()
        df['DIF'] = ema12 - ema26
        df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
        df['MACD'] = (df['DIF'] - df['DEA']) * 2
        return df
        
    def calculate_rsi(self, df: pd.DataFrame, window: int = 14) -> pd.DataFrame:
        """计算RSI指标"""
        delta = df['收盘'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=window).mean()
        avg_loss = loss.rolling(window=window).mean()
        rs = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + rs))
        return df
        
    def calculate_kdj(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算KDJ指标"""
        low_min = df['最低'].rolling(window=9).min()
        high_max = df['最高'].rolling(window=9).max()
        rsv = (df['收盘'] - low_min) / (high_max - low_min) * 100
        df['K'] = rsv.ewm(com=2).mean()
        df['D'] = df['K'].ewm(com=2).mean()
        df['J'] = 3 * df['K'] - 2 * df['D']
        return df
        
    def calculate_boll(self, df: pd.DataFrame, window: int = 20) -> pd.DataFrame:
        """计算布林带指标"""
        df['BOLL_MID'] = df['收盘'].rolling(window=window).mean()
        std = df['收盘'].rolling(window=window).std()
        df['BOLL_UPPER'] = df['BOLL_MID'] + 2 * std
        df['BOLL_LOWER'] = df['BOLL_MID'] - 2 * std
        return df
    
    def _get_data_from_akshare(self, code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """从akshare获取数据"""
        try:
            df = self.akshare_provider.get_stock_history(
                code=code,
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"
            )
            if df is not None and not df.empty:
                return self._standardize_columns(df)
        except Exception as e:
            self.logger.warning(f"从akshare获取数据失败: {str(e)}")
        return None
    
    
    
    
    
    
            

    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化数据列名和格式"""
        # 确保日期列统一为'日期'
        if 'date' in df.columns and '日期' not in df.columns:
            df = df.rename(columns={'date': '日期'})
        
        # 统一日期格式
        if '日期' in df.columns:
            df['日期'] = pd.to_datetime(df['日期'])
        
        # 确保必要的列存在
        required_columns = ['开盘', '收盘', '最高', '最低', '成交量']
        for col in required_columns:
            if col not in df.columns:
                df[col] = np.nan
        
        # 处理可选列
        optional_columns = ['成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
        for col in optional_columns:
            if col not in df.columns:
                df[col] = np.nan
        
        return df
    
    def get_stock_data(self, code: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """获取股票历史数据，尝试多个数据源"""
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        
        # 依次尝试各个数据源
        for data_source in self.data_sources:
            try:
                df = data_source(code, start_date, end_date)
                if df is not None and not df.empty:
                    self.logger.info(f"成功从{data_source.__name__}获取股票{code}数据")
                    return df
            except Exception as e:
                self.logger.warning(f"数据源{data_source.__name__}获取失败: {str(e)}")
                continue
        
        self.logger.error(f"所有数据源都无法获取股票{code}数据")
        return None
    
    def get_stock_info(self, code: str) -> Optional[Dict]:
        """获取股票基本信息，尝试多个数据源"""
        try:
            # 首先尝试从akshare获取
            stock_info = self.akshare_provider.get_stock_info(code)
            if stock_info is not None:
                return stock_info
                
            # 如果akshare获取失败，尝试其他数据源
            df = self.akshare_provider.get_all_stock_list()
            if df is not None and not df.empty:
                stock_info = df[df['代码'] == code].iloc[0]
                
                # 获取更多指标
                try:
                    indicator_df = self.akshare_provider.get_stock_financial_indicator(code)
                except:
                    indicator_df = pd.DataFrame()
                
                info = {
                    'code': code,
                    'name': stock_info['名称'],
                    'current_price': float(stock_info['最新价']),
                    'change_percent': float(stock_info['涨跌幅']),
                    'open_price': float(stock_info['开盘']),
                    'prev_close': float(stock_info['昨收']),
                    'high_price': float(stock_info['最高']),
                    'low_price': float(stock_info['最低']),
                    'volume': float(stock_info['成交量']),
                    'amount': float(stock_info['成交额']),
                    'turnover_rate': float(stock_info['换手率']),
                    'pe_ratio': float(indicator_df['市盈率'].iloc[-1]) if not indicator_df.empty else None,
                    'pb_ratio': float(indicator_df['市净率'].iloc[-1]) if not indicator_df.empty else None,
                    'total_value': float(stock_info['总市值']) if '总市值' in stock_info else None,
                    'industry': stock_info['所属行业'] if '所属行业' in stock_info else None
                }
                return info
            
            # 如果akshare失败，尝试其他数据源
            # TODO: 实现其他数据源的股票信息获取
            
        except Exception as e:
            self.logger.error(f"获取股票{code}基本信息失败: {str(e)}")
        return None
    
    def get_stock_list(self) -> List[Dict[str, str]]:
        """获取股票列表"""
        try:
            df = self.akshare_provider.get_all_stock_list()
            if df is not None and not df.empty:
                stock_list = []
                for _, row in df.iterrows():
                    stock_list.append({
                        'code': row['代码'],
                        'name': row['名称']
                    })
                return stock_list
            return []
        except Exception as e:
            self.logger.error(f"获取股票列表失败: {str(e)}")
            return []
            
    def get_index_stocks(self, index_code: str) -> Optional[List[str]]:
        """获取指数成分股"""
        try:
            codes = self.akshare_provider.get_index_stocks(index_code)
            if codes is not None:
                return codes
            return None
        except Exception as e:
            self.logger.error(f"获取指数{index_code}成分股失败: {str(e)}")
            return None
    
    def get_industry_stocks(self, industry_code: str) -> Optional[List[str]]:
        """获取行业成分股"""
        try:
            # 调用akshare API获取行业成分股
            stocks_df = self.akshare_provider._retry_wrapper(
                ak.stock_board_industry_cons_em,
                symbol=industry_code
            )
            
            if stocks_df is not None and not stocks_df.empty:
                return stocks_df['代码'].tolist()
            return None
        except Exception as e:
            self.logger.error(f"获取行业{industry_code}成分股失败: {str(e)}")
            return None
    
    def get_total_value(self, code: str) -> Optional[float]:
        """获取股票总市值（单位：元）"""
        try:
            # 尝试获取股票实时行情
            stock_info = self.akshare_provider.get_stock_real_time_quote(code)
            if stock_info is not None and '总市值' in stock_info:
                return float(stock_info['总市值'])
            
            # 尝试从东方财富获取
            market = "0" if code.startswith('6') else "1"
            url = "http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'secid': f"{market}.{code}",
                'fields': 'f116'  # 总市值
            }
            response = requests.get(url, params=params)
            data = response.json()
            
            if data['data'] and 'f116' in data['data']:
                return float(data['data']['f116']) * 10000  # 转换为元
                
            return None
            
        except Exception as e:
            self.logger.error(f"获取股票{code}总市值失败: {str(e)}")
            return None
    
    def get_industry(self, code: str) -> Optional[str]:
        """获取股票所属行业"""
        try:
            # 尝试从AKShare数据提供者获取
            return self.akshare_provider.get_stock_industry(code)
            
        except Exception as e:
            self.logger.error(f"获取股票{code}所属行业失败: {str(e)}")
            return None