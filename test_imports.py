"""
测试导入问题
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("开始测试导入...")

try:
    print("1. 测试导入 StockData...")
    from src.models.stock import StockData
    print("OK StockData 导入成功")
except Exception as e:
    print(f"ERROR StockData 导入失败: {e}")

try:
    print("2. 测试导入 AKShareDataProvider...")
    from src.data_providers.akshare_data_provider import AKShareDataProvider
    print("OK AKShareDataProvider 导入成功")
except Exception as e:
    print(f"ERROR AKShareDataProvider 导入失败: {e}")

try:
    print("3. 测试导入 StockDataLoader...")
    from src.models.data_loader import StockDataLoader
    print("OK StockDataLoader 导入成功")
except Exception as e:
    print(f"ERROR StockDataLoader 导入失败: {e}")

try:
    print("4. 测试导入 StockService...")
    from src.services.stock_service import StockService
    print("OK StockService 导入成功")
except Exception as e:
    print(f"ERROR StockService 导入失败: {e}")

try:
    print("5. 测试导入 MainWindow...")
    from src.ui.main_window import MainWindow
    print("OK MainWindow 导入成功")
except Exception as e:
    print(f"ERROR MainWindow 导入失败: {e}")

print("导入测试完成")
