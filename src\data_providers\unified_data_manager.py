"""
统一数据管理器
整合多个数据源，提供统一的数据访问接口
"""

import logging
import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .enhanced_data_provider import DataRequest, DataSource
from .akshare_enhanced_provider import AKShareEnhancedProvider


class UnifiedDataManager:
    """统一数据管理器"""
    
    def __init__(self, 
                 cache_dir: str = "data/cache",
                 cache_ttl: int = 3600,
                 max_workers: int = 4,
                 enable_quality_check: bool = True):
        """
        初始化统一数据管理器
        
        Args:
            cache_dir: 缓存目录
            cache_ttl: 缓存生存时间(秒)
            max_workers: 最大并发工作线程数
            enable_quality_check: 是否启用数据质量检查
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化数据提供者
        self.providers = {
            DataSource.AKSHARE: AKShareEnhancedProvider(
                cache_dir=cache_dir,
                cache_ttl=cache_ttl,
                max_workers=max_workers,
                enable_quality_check=enable_quality_check
            )
        }
        
        # 数据源优先级配置
        self.source_priority = {
            'stock_list': [DataSource.AKSHARE],
            'daily_data': [DataSource.AKSHARE],
            'realtime_data': [DataSource.AKSHARE],
            'minute_data': [DataSource.AKSHARE],
            'financial_data': [DataSource.AKSHARE],
            'technical_indicators': [DataSource.AKSHARE],
            'market_summary': [DataSource.AKSHARE],
            'sector_data': [DataSource.AKSHARE],
            'news_data': [DataSource.AKSHARE],
            'dividend_data': [DataSource.AKSHARE]
        }
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'data_sources_used': {}
        }
        
        self.logger.info("统一数据管理器初始化完成")
    
    def get_stock_list(self, market: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 ('SSE', 'SZSE', 'BSE' 或 None 表示全部)
            
        Returns:
            股票列表DataFrame
        """
        request = DataRequest(
            symbol="",
            data_type="stock_list",
            extra_params={'market': market}
        )
        
        data = self._get_data_with_fallback(request)
        
        if data is not None and market:
            # 按市场过滤
            data = data[data['market'] == market]
        
        return data
    
    def get_daily_data(self, 
                      symbol: str, 
                      start_date: Optional[str] = None, 
                      end_date: Optional[str] = None,
                      adjust: str = "qfq") -> Optional[pd.DataFrame]:
        """
        获取日线数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            adjust: 复权类型 ('qfq', 'hfq', 'none')
            
        Returns:
            日线数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="daily_data",
            start_date=start_date,
            end_date=end_date,
            extra_params={'adjust': adjust}
        )
        
        return self._get_data_with_fallback(request)
    
    def get_realtime_data(self, symbols: Union[str, List[str]]) -> Optional[pd.DataFrame]:
        """
        获取实时数据
        
        Args:
            symbols: 股票代码或股票代码列表
            
        Returns:
            实时数据DataFrame
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        results = []
        
        # 并发获取多个股票的实时数据
        with ThreadPoolExecutor(max_workers=min(len(symbols), 10)) as executor:
            future_to_symbol = {}
            
            for symbol in symbols:
                request = DataRequest(
                    symbol=symbol,
                    data_type="realtime_data"
                )
                future = executor.submit(self._get_data_with_fallback, request)
                future_to_symbol[future] = symbol
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    if data is not None and not data.empty:
                        results.append(data)
                except Exception as e:
                    self.logger.error(f"获取 {symbol} 实时数据失败: {e}")
        
        if results:
            return pd.concat(results, ignore_index=True)
        
        return None
    
    def get_minute_data(self, 
                       symbol: str, 
                       period: str = "1",
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        获取分钟数据
        
        Args:
            symbol: 股票代码
            period: 周期 ('1', '5', '15', '30', '60')
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            分钟数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="minute_data",
            period=period,
            start_date=start_date,
            end_date=end_date
        )
        
        return self._get_data_with_fallback(request)
    
    def get_financial_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取财务数据
        
        Args:
            symbol: 股票代码
            
        Returns:
            财务数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="financial_data"
        )
        
        return self._get_data_with_fallback(request)
    
    def get_technical_indicators(self, 
                               symbol: str,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None,
                               indicators: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        获取技术指标数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            indicators: 指标列表
            
        Returns:
            技术指标数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="technical_indicators",
            start_date=start_date,
            end_date=end_date,
            extra_params={'indicators': indicators}
        )
        
        return self._get_data_with_fallback(request)
    
    def get_market_summary(self) -> Optional[pd.DataFrame]:
        """
        获取市场概况
        
        Returns:
            市场概况DataFrame
        """
        request = DataRequest(
            symbol="",
            data_type="market_summary"
        )
        
        return self._get_data_with_fallback(request)
    
    def get_sector_data(self, sector_type: str = "concept") -> Optional[pd.DataFrame]:
        """
        获取板块数据
        
        Args:
            sector_type: 板块类型 ('concept', 'industry')
            
        Returns:
            板块数据DataFrame
        """
        request = DataRequest(
            symbol="",
            data_type="sector_data",
            extra_params={'sector_type': sector_type}
        )
        
        return self._get_data_with_fallback(request)
    
    def get_news_data(self, symbol: str, limit: int = 20) -> Optional[pd.DataFrame]:
        """
        获取新闻数据
        
        Args:
            symbol: 股票代码
            limit: 新闻条数限制
            
        Returns:
            新闻数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="news_data",
            extra_params={'limit': limit}
        )
        
        return self._get_data_with_fallback(request)
    
    def get_dividend_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取分红数据
        
        Args:
            symbol: 股票代码
            
        Returns:
            分红数据DataFrame
        """
        request = DataRequest(
            symbol=symbol,
            data_type="dividend_data"
        )
        
        return self._get_data_with_fallback(request)
    
    def _get_data_with_fallback(self, request: DataRequest) -> Optional[pd.DataFrame]:
        """使用备用数据源获取数据"""
        with self.lock:
            self.stats['total_requests'] += 1
        
        data_type = request.data_type
        sources = self.source_priority.get(data_type, [DataSource.AKSHARE])
        
        for source in sources:
            if source not in self.providers:
                continue
            
            try:
                provider = self.providers[source]
                data = provider.get_data(request)
                
                if data is not None and not data.empty:
                    with self.lock:
                        self.stats['successful_requests'] += 1
                        if source.value not in self.stats['data_sources_used']:
                            self.stats['data_sources_used'][source.value] = 0
                        self.stats['data_sources_used'][source.value] += 1
                    
                    self.logger.debug(f"成功从 {source.value} 获取数据: {request.symbol}")
                    return data
                
            except Exception as e:
                self.logger.warning(f"从 {source.value} 获取数据失败: {e}")
                continue
        
        with self.lock:
            self.stats['failed_requests'] += 1
        
        self.logger.error(f"所有数据源都无法获取数据: {request.symbol} - {request.data_type}")
        return None
    
    def batch_get_daily_data(self, 
                           symbols: List[str],
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           max_workers: int = 5) -> Dict[str, pd.DataFrame]:
        """
        批量获取日线数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            max_workers: 最大并发数
            
        Returns:
            股票代码到数据的映射字典
        """
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_symbol = {}
            
            for symbol in symbols:
                future = executor.submit(
                    self.get_daily_data, 
                    symbol, 
                    start_date, 
                    end_date
                )
                future_to_symbol[future] = symbol
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    if data is not None and not data.empty:
                        results[symbol] = data
                except Exception as e:
                    self.logger.error(f"批量获取 {symbol} 数据失败: {e}")
        
        return results
    
    def clear_cache(self, pattern: Optional[str] = None) -> None:
        """清理所有提供者的缓存"""
        for provider in self.providers.values():
            provider.clear_cache(pattern)
        
        self.logger.info("已清理所有缓存")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        provider_stats = {}
        for source, provider in self.providers.items():
            provider_stats[source.value] = provider.get_stats()
        
        return {
            'manager_stats': self.stats,
            'provider_stats': provider_stats
        }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'status': 'healthy',
            'providers': {},
            'timestamp': datetime.now().isoformat()
        }
        
        # 检查各个数据提供者
        for source, provider in self.providers.items():
            try:
                # 尝试获取股票列表作为健康检查
                test_request = DataRequest(symbol="", data_type="stock_list")
                test_data = provider.get_data(test_request)
                
                if test_data is not None and not test_data.empty:
                    health_status['providers'][source.value] = {
                        'status': 'healthy',
                        'data_count': len(test_data)
                    }
                else:
                    health_status['providers'][source.value] = {
                        'status': 'unhealthy',
                        'error': 'No data returned'
                    }
                    health_status['status'] = 'degraded'
                    
            except Exception as e:
                health_status['providers'][source.value] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
                health_status['status'] = 'degraded'
        
        return health_status
