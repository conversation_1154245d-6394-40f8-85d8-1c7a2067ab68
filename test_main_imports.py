"""
测试main.py的导入问题
"""

import sys
import os

print("开始测试main.py的导入...")

try:
    print("1. 测试基本导入...")
    import warnings
    import logging
    print("OK 基本导入成功")
    
    print("2. 测试TensorFlow导入...")
    import tensorflow as tf
    print("OK TensorFlow导入成功")
    
    print("3. 测试设置警告过滤...")
    warnings.filterwarnings('ignore')
    print("OK 警告过滤设置成功")
    
    print("4. 测试PyQt5导入...")
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    print("OK PyQt5导入成功")
    
    print("5. 测试项目路径...")
    sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
    print("OK 项目路径设置成功")
    
    print("6. 测试MainWindow导入...")
    from src.ui.main_window import MainWindow
    print("OK MainWindow导入成功")
    
    print("7. 测试创建QApplication...")
    app = QApplication([])
    print("OK QApplication创建成功")
    
    print("8. 测试创建MainWindow...")
    window = MainWindow()
    print("OK MainWindow创建成功")
    
    print("所有测试通过！")
    
except Exception as e:
    print(f"ERROR 测试失败: {e}")
    import traceback
    traceback.print_exc()
