import sys
import os
import warnings
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 配置日志级别
logging.basicConfig(
    level=logging.WARNING,
    format='%(levelname)s: %(message)s'
)

# 过滤特定模块的日志
logging.getLogger('src.ui').setLevel(logging.ERROR)
logging.getLogger('src.services').setLevel(logging.WARNING)
logging.getLogger('src.models').setLevel(logging.WARNING)

# 过滤警告信息
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', message='.*force_all_finite.*')
warnings.filterwarnings('ignore', message='.*warm_start.*')
warnings.filterwarnings('ignore', message='.*No further splits with positive gain.*')
warnings.filterwarnings('ignore', message='.*out-of-bounds.*')

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from src.ui.main_window import MainWindow

def main():
    """主函数"""
    try:
        print("正在启动AI智能选股系统...")

        # 创建应用程序
        print("创建应用程序...")
        app = QApplication(sys.argv)

        # 设置应用程序样式
        print("设置应用程序样式...")
        app.setStyle('Fusion')

        # 创建并显示主窗口
        print("创建主窗口...")
        window = MainWindow()

        print("显示主窗口...")
        window.show()

        print("启动应用程序事件循环...")
        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        print("程序运行出错，请检查错误信息。")
        input("按任意键退出...")
        sys.exit(1)

if __name__ == '__main__':
    main()
