"""
测试修复后的数据提供者
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_providers.unified_data_manager import UnifiedDataManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_stock_list():
    """测试股票列表获取"""
    logger.info("=== 测试股票列表获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 获取股票列表
        stock_list = manager.get_stock_list()
        
        if stock_list is not None and not stock_list.empty:
            logger.info(f"✅ 成功获取股票列表，共 {len(stock_list)} 只股票")
            
            # 获取一些测试用的股票代码
            test_symbols = stock_list['symbol'].head(5).tolist()
            logger.info(f"测试股票代码: {test_symbols}")
            
            return True, test_symbols
        else:
            logger.error("❌ 获取股票列表失败")
            return False, []
            
    except Exception as e:
        logger.error(f"❌ 测试股票列表获取异常: {e}")
        return False, []


def test_daily_data(test_symbols):
    """测试日线数据获取"""
    logger.info("=== 测试日线数据获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 设置较短的日期范围
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=10)).strftime("%Y%m%d")
        
        success_count = 0
        
        # 只测试前3个股票
        for symbol in test_symbols[:3]:
            logger.info(f"测试获取 {symbol} 的日线数据...")
            
            try:
                daily_data = manager.get_daily_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date
                )
                
                if daily_data is not None and not daily_data.empty:
                    logger.info(f"✅ 成功获取 {symbol} 日线数据，共 {len(daily_data)} 条记录")
                    logger.info(f"列名: {list(daily_data.columns)}")
                    success_count += 1
                else:
                    logger.warning(f"⚠️ 获取 {symbol} 日线数据为空")
                    
            except Exception as e:
                logger.error(f"❌ 获取 {symbol} 日线数据异常: {e}")
        
        logger.info(f"日线数据测试完成，成功率: {success_count}/3")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 测试日线数据获取异常: {e}")
        return False


def test_realtime_data(test_symbols):
    """测试实时数据获取"""
    logger.info("=== 测试实时数据获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 只测试前2个股票
        test_symbols_short = test_symbols[:2]
        
        realtime_data = manager.get_realtime_data(test_symbols_short)
        
        if realtime_data is not None and not realtime_data.empty:
            logger.info(f"✅ 成功获取实时数据，共 {len(realtime_data)} 条记录")
            logger.info(f"列名: {list(realtime_data.columns)}")
            logger.info(f"实时数据:\n{realtime_data}")
            return True
        else:
            logger.warning("⚠️ 获取实时数据为空")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试实时数据获取异常: {e}")
        return False


def test_market_summary():
    """测试市场概况获取"""
    logger.info("=== 测试市场概况获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        market_data = manager.get_market_summary()
        
        if market_data is not None and not market_data.empty:
            logger.info(f"✅ 成功获取市场概况数据")
            logger.info(f"列名: {list(market_data.columns)}")
            return True
        else:
            logger.warning("⚠️ 获取市场概况为空")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试市场概况获取异常: {e}")
        return False


def test_cache_functionality():
    """测试缓存功能"""
    logger.info("=== 测试缓存功能 ===")
    
    try:
        manager = UnifiedDataManager(cache_ttl=300)  # 5分钟缓存
        
        # 第一次获取数据
        logger.info("第一次获取股票列表...")
        start_time = datetime.now()
        data1 = manager.get_stock_list()
        first_duration = (datetime.now() - start_time).total_seconds()
        
        # 第二次获取相同数据（应该从缓存获取）
        logger.info("第二次获取股票列表...")
        start_time = datetime.now()
        data2 = manager.get_stock_list()
        second_duration = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"第一次耗时: {first_duration:.2f}秒")
        logger.info(f"第二次耗时: {second_duration:.2f}秒")
        
        # 获取统计信息
        stats = manager.get_stats()
        logger.info(f"统计信息: {stats}")
        
        # 缓存应该使第二次更快
        if second_duration < first_duration * 0.5:
            logger.info("✅ 缓存功能正常工作")
            return True
        else:
            logger.warning("⚠️ 缓存功能可能未正常工作")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试缓存功能异常: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    logger.info("=== 测试健康检查 ===")
    
    try:
        manager = UnifiedDataManager()
        
        health = manager.health_check()
        logger.info(f"健康检查结果: {health}")
        
        if health['status'] in ['healthy', 'degraded']:
            logger.info("✅ 健康检查通过")
            return True
        else:
            logger.warning("⚠️ 健康检查未通过")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试健康检查异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试修复后的数据提供者功能")
    
    test_results = []
    test_symbols = []
    
    # 首先获取测试用的股票代码
    stock_list_success, test_symbols = test_stock_list()
    test_results.append(("股票列表获取", stock_list_success))
    
    if stock_list_success and test_symbols:
        # 运行其他测试
        tests = [
            ("日线数据获取", lambda: test_daily_data(test_symbols)),
            ("实时数据获取", lambda: test_realtime_data(test_symbols)),
            ("市场概况获取", test_market_summary),
            ("缓存功能", test_cache_functionality),
            ("健康检查", test_health_check)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始测试: {test_name}")
            
            try:
                result = test_func()
                test_results.append((test_name, result))
                
                if result:
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
                test_results.append((test_name, False))
    else:
        logger.error("无法获取测试股票代码，跳过其他测试")
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
    elif passed > total * 0.5:
        logger.info("✅ 大部分测试通过，数据获取功能基本正常")
    else:
        logger.warning(f"⚠️ 有 {total - passed} 项测试失败，需要进一步优化")


if __name__ == "__main__":
    main()
