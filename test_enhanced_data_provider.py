"""
测试增强数据提供者功能
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_providers.unified_data_manager import UnifiedDataManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_data_provider.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


def test_stock_list():
    """测试股票列表获取"""
    logger.info("=== 测试股票列表获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 获取股票列表
        stock_list = manager.get_stock_list()
        
        if stock_list is not None and not stock_list.empty:
            logger.info(f"成功获取股票列表，共 {len(stock_list)} 只股票")
            logger.info(f"列名: {list(stock_list.columns)}")
            logger.info(f"前5只股票:\n{stock_list.head()}")
            return True
        else:
            logger.error("获取股票列表失败")
            return False
            
    except Exception as e:
        logger.error(f"测试股票列表获取异常: {e}")
        return False


def test_daily_data():
    """测试日线数据获取"""
    logger.info("=== 测试日线数据获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 测试股票代码
        test_symbols = ['000001', '000002', '600000', '600036']
        
        # 设置日期范围
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
        
        success_count = 0
        
        for symbol in test_symbols:
            logger.info(f"测试获取 {symbol} 的日线数据...")
            
            daily_data = manager.get_daily_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if daily_data is not None and not daily_data.empty:
                logger.info(f"成功获取 {symbol} 日线数据，共 {len(daily_data)} 条记录")
                logger.info(f"列名: {list(daily_data.columns)}")
                logger.info(f"最新数据:\n{daily_data.tail(1)}")
                success_count += 1
            else:
                logger.error(f"获取 {symbol} 日线数据失败")
        
        logger.info(f"日线数据测试完成，成功率: {success_count}/{len(test_symbols)}")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"测试日线数据获取异常: {e}")
        return False


def test_realtime_data():
    """测试实时数据获取"""
    logger.info("=== 测试实时数据获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 测试股票代码
        test_symbols = ['000001', '600000']
        
        realtime_data = manager.get_realtime_data(test_symbols)
        
        if realtime_data is not None and not realtime_data.empty:
            logger.info(f"成功获取实时数据，共 {len(realtime_data)} 条记录")
            logger.info(f"列名: {list(realtime_data.columns)}")
            logger.info(f"实时数据:\n{realtime_data}")
            return True
        else:
            logger.error("获取实时数据失败")
            return False
            
    except Exception as e:
        logger.error(f"测试实时数据获取异常: {e}")
        return False


def test_technical_indicators():
    """测试技术指标获取"""
    logger.info("=== 测试技术指标获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 设置日期范围
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=100)).strftime("%Y%m%d")
        
        # 获取技术指标
        tech_data = manager.get_technical_indicators(
            symbol='000001',
            start_date=start_date,
            end_date=end_date
        )
        
        if tech_data is not None and not tech_data.empty:
            logger.info(f"成功获取技术指标数据，共 {len(tech_data)} 条记录")
            logger.info(f"列名: {list(tech_data.columns)}")
            
            # 检查是否包含技术指标
            indicator_columns = [col for col in tech_data.columns 
                               if any(indicator in col.lower() 
                                    for indicator in ['ma', 'macd', 'rsi', 'bb'])]
            logger.info(f"技术指标列: {indicator_columns}")
            
            if indicator_columns:
                logger.info(f"最新技术指标:\n{tech_data[indicator_columns].tail(1)}")
            
            return True
        else:
            logger.error("获取技术指标数据失败")
            return False
            
    except Exception as e:
        logger.error(f"测试技术指标获取异常: {e}")
        return False


def test_market_summary():
    """测试市场概况获取"""
    logger.info("=== 测试市场概况获取 ===")
    
    try:
        manager = UnifiedDataManager()
        
        market_data = manager.get_market_summary()
        
        if market_data is not None and not market_data.empty:
            logger.info(f"成功获取市场概况数据")
            logger.info(f"列名: {list(market_data.columns)}")
            logger.info(f"市场概况:\n{market_data}")
            return True
        else:
            logger.error("获取市场概况失败")
            return False
            
    except Exception as e:
        logger.error(f"测试市场概况获取异常: {e}")
        return False


def test_batch_operations():
    """测试批量操作"""
    logger.info("=== 测试批量操作 ===")
    
    try:
        manager = UnifiedDataManager()
        
        # 批量获取日线数据
        symbols = ['000001', '000002', '600000', '600036', '000858']
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
        
        batch_data = manager.batch_get_daily_data(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            max_workers=3
        )
        
        if batch_data:
            logger.info(f"批量获取成功，获得 {len(batch_data)} 只股票的数据")
            for symbol, data in batch_data.items():
                logger.info(f"{symbol}: {len(data)} 条记录")
            return True
        else:
            logger.error("批量获取失败")
            return False
            
    except Exception as e:
        logger.error(f"测试批量操作异常: {e}")
        return False


def test_cache_and_stats():
    """测试缓存和统计功能"""
    logger.info("=== 测试缓存和统计功能 ===")
    
    try:
        manager = UnifiedDataManager(cache_ttl=60)  # 1分钟缓存
        
        # 第一次获取数据
        logger.info("第一次获取数据...")
        data1 = manager.get_daily_data('000001')
        
        # 第二次获取相同数据（应该从缓存获取）
        logger.info("第二次获取相同数据...")
        data2 = manager.get_daily_data('000001')
        
        # 获取统计信息
        stats = manager.get_stats()
        logger.info(f"统计信息: {stats}")
        
        # 健康检查
        health = manager.health_check()
        logger.info(f"健康检查: {health}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试缓存和统计功能异常: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试增强数据提供者功能")
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("股票列表获取", test_stock_list),
        ("日线数据获取", test_daily_data),
        ("实时数据获取", test_realtime_data),
        ("技术指标获取", test_technical_indicators),
        ("市场概况获取", test_market_summary),
        ("批量操作", test_batch_operations),
        ("缓存和统计", test_cache_and_stats)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        
        try:
            result = test_func()
            test_results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结:")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试都通过了！")
    else:
        logger.warning(f"⚠️  有 {total - passed} 项测试失败")


if __name__ == "__main__":
    main()
