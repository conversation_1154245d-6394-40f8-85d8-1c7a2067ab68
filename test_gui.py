"""
测试GUI启动
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

print("开始测试GUI...")

try:
    print("1. 测试导入 PyQt5...")
    from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
    from PyQt5.QtCore import Qt
    print("OK PyQt5 导入成功")
    
    print("2. 创建应用程序...")
    app = QApplication(sys.argv)
    print("OK 应用程序创建成功")
    
    print("3. 创建测试窗口...")
    window = QWidget()
    window.setWindowTitle("测试窗口")
    window.setGeometry(100, 100, 300, 200)
    
    layout = QVBoxLayout()
    label = QLabel("GUI测试成功！")
    label.setAlignment(Qt.AlignCenter)
    layout.addWidget(label)
    window.setLayout(layout)
    
    print("OK 测试窗口创建成功")
    
    print("4. 显示窗口...")
    window.show()
    print("OK 窗口显示成功")
    
    print("GUI测试完成，窗口应该已经显示")
    print("请关闭窗口以退出程序")
    
    # 运行应用程序
    sys.exit(app.exec_())
    
except Exception as e:
    print(f"ERROR GUI测试失败: {e}")
    import traceback
    traceback.print_exc()
