import logging
import time
from typing import Optional, Dict, Any, List
import akshare as ak
import numpy as np
import pandas as pd
import os

# 在导入yfinance之前设置环境变量，以解决SSL证书问题
os.environ['CURL_CA_BUNDLE'] = ''

from curl_cffi import requests as curl_requests
import yfinance as yf
# 避免循环导入，使用延迟导入
# from src.models.stock import StockData

class AKShareDataProvider:
    """数据提供者，支持多种数据源并增加了重试机制"""
    
    def __init__(self, max_retries=3, retry_wait=2):
        self.cache: Dict[str, Any] = {}
        self.cache_timeout = 300  # 缓存超时时间（秒）
        self.cache_timestamps: Dict[str, float] = {}
        self.max_retries = max_retries
        self.retry_wait = retry_wait
        self.logger = logging.getLogger(__name__)
        # 创建一个禁用SSL验证的curl_cffi会话
        self.cffi_session = curl_requests.Session(verify=False, impersonate="chrome110")

    def _is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        if key not in self.cache_timestamps:
            return False
        return time.time() - self.cache_timestamps[key] < self.cache_timeout

    def _update_cache(self, key: str, value: Any):
        """更新缓存"""
        self.cache[key] = value
        self.cache_timestamps[key] = time.time()

    def _retry_wrapper(self, func, *args, **kwargs):
        """包装函数，添加重试逻辑"""
        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                if result is not None and not isinstance(result, pd.DataFrame) or (isinstance(result, pd.DataFrame) and not result.empty):
                    return result
                self.logger.warning(f"{func.__name__} attempt {attempt + 1} returned empty result.")
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1}/{self.max_retries} for {func.__name__} failed: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_wait * (attempt + 1))
                else:
                    self.logger.error(f"All attempts for {func.__name__} failed.")
        return None

    def get_stock_history(self, code: str) -> Optional[pd.DataFrame]:
        """获取股票历史数据，使用yfinance作为唯一数据源"""
        self.logger.info(f"Fetching history for {code} using yfinance.")
        data = self._retry_wrapper(self._get_history_from_yfinance, code)
        if data is not None and not data.empty:
            self.logger.info(f"Successfully fetched data for {code} from yfinance")
            return self._standardize_history_columns(data)
        
        self.logger.error(f"Failed to fetch history for {code} from yfinance.")
        return None

    def _get_history_from_yfinance(self, code: str) -> Optional[pd.DataFrame]:
        """使用yfinance获取历史数据"""
        market = '.SS' if code.startswith('6') else '.SZ'
        ticker = yf.Ticker(f"{code}{market}", session=self.cffi_session)
        hist = ticker.history(period="5y") # 获取最近5年的数据
        if not hist.empty:
            hist.reset_index(inplace=True)
            hist.rename(columns={'Date': '日期', 'Open': '开盘', 'High': '最高', 'Low': '最低', 'Close': '收盘', 'Volume': '成交量'}, inplace=True)
            return hist
        return None

    def _get_history_from_akshare_hist(self, code: str) -> Optional[pd.DataFrame]:
        return ak.stock_zh_a_hist(symbol=code, period="daily", adjust="qfq")

    def _get_history_from_eastmoney(self, code: str) -> Optional[pd.DataFrame]:
        market = '1' if code.startswith('6') else '0'
        url = f"http://push2his.eastmoney.com/api/qt/stock/kline/get?secid={market}.{code}&klt=101&fqt=1&lmt=1000"
        r = self.session.get(url, timeout=5)
        data = r.json()
        if not data or 'data' not in data or not data['data'] or 'klines' not in data['data']:
            return None
        df = pd.DataFrame([item.split(',') for item in data['data']['klines']])
        df.columns = ['日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额', '振幅', '涨跌幅', '涨跌额', '换手率']
        return df

    def _get_history_from_tencent(self, code: str) -> Optional[pd.DataFrame]:
        market = 'sh' if code.startswith('6') else 'sz'
        url = f"http://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param={market}{code},day,,,1000,qfq"
        r = self.session.get(url, timeout=5)
        data = r.json()
        key = f"qfqday"
        if 'data' not in data or f"{market}{code}" not in data['data'] or key not in data['data'][f"{market}{code}"]:
            return None
        df = pd.DataFrame(data['data'][f"{market}{code}"][key])
        if df.empty:
            return None
        df.columns = ['日期', '开盘', '收盘', '最高', '最低', '成交量', '成交额', '换手率']
        return df

    def _standardize_history_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        column_map = {
            '日期': 'date', '开盘': 'open', '收盘': 'close', '最高': 'high', '最低': 'low',
            '成交量': 'volume', '成交额': 'amount', '换手率': 'turnover'
        }
        df = df.rename(columns=column_map)
        for col in ['open', 'close', 'high', 'low', 'volume', 'amount', 'turnover']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        df['date'] = pd.to_datetime(df['date'])
        return df

    def get_stock_info(self, code: str) -> Optional[Any]:
        """获取单只股票的详细信息，改用yfinance实现"""
        # 延迟导入避免循环导入
        from src.models.stock import StockData

        cache_key = f"stock_info_{code}"
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]

        try:
            market = '.SS' if code.startswith('6') else '.SZ'
            ticker = yf.Ticker(f"{code}{market}", session=self.cffi_session)
            
            info = ticker.info
            hist = ticker.history(period="2d")

            if info is None or hist.empty:
                self.logger.warning(f"未找到股票 {code} 的信息 (yfinance)")
                return None

            # 确保有足够的数据来计算变化
            if len(hist) < 2:
                change_percent = 0.0
                prev_close = hist.iloc[0]['Close'] # 如果只有一天数据，昨收就是今天的收盘
            else:
                change_percent = (hist.iloc[-1]['Close'] / hist.iloc[-2]['Close'] - 1) * 100
                prev_close = hist.iloc[-2]['Close']

            stock = StockData(
                code=code,
                name=info.get('shortName', ''),
                current_price=float(hist.iloc[-1]['Close']),
                change_percent=float(change_percent),
                open_price=float(hist.iloc[-1]['Open']),
                high_price=float(hist.iloc[-1]['High']),
                low_price=float(hist.iloc[-1]['Low']),
                prev_close=float(prev_close),
                volume=float(hist.iloc[-1]['Volume']),
                amount=info.get('turnover', 0) * hist.iloc[-1]['Close'], # yfinance的turnover是换手率，乘以收盘价估算成交额
                turnover_rate=info.get('sharesPercentSharesOut', 0) * 100 if info.get('sharesPercentSharesOut') else 0,
                pe_ratio=info.get('trailingPE', 0),
                total_value=info.get('marketCap', 0)
            )
            self._update_cache(cache_key, stock)
            return stock
        except Exception as e:
            self.logger.error(f"使用yfinance获取股票 {code} 信息时出错: {e}")
            return None

    def get_all_stock_list(self) -> Optional[pd.DataFrame]:
        """获取所有股票列表"""
        cache_key = "all_stock_list"
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]
        
        # 使用 stock_info_a_code_name 获取更稳定的股票列表
        stock_list = self._retry_wrapper(ak.stock_info_a_code_name)
        if stock_list is not None:
            self._update_cache(cache_key, stock_list)
        return stock_list
 