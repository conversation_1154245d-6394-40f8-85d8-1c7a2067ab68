"""
简化版数据获取功能演示
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_providers.unified_data_manager import UnifiedDataManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主演示函数"""
    print("智能股票分析系统 - 数据获取功能演示")
    print("="*60)
    
    try:
        manager = UnifiedDataManager()
        
        # 1. 获取股票列表
        print("\n1. 获取股票列表...")
        stock_list = manager.get_stock_list()
        
        if stock_list is not None and not stock_list.empty:
            print(f"成功获取 {len(stock_list)} 只股票")
            print(f"数据列: {list(stock_list.columns)}")
            
            # 获取测试股票代码
            test_symbols = stock_list['symbol'].head(3).tolist()
            print(f"测试股票: {test_symbols}")
            
            # 2. 获取日线数据
            print(f"\n2. 获取日线数据...")
            symbol = test_symbols[0]
            end_date = datetime.now().strftime("%Y%m%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y%m%d")
            
            daily_data = manager.get_daily_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if daily_data is not None and not daily_data.empty:
                print(f"成功获取 {symbol} 的 {len(daily_data)} 条日线数据")
                print(f"数据列: {list(daily_data.columns)}")
                
                # 显示最新数据
                if len(daily_data) > 0:
                    latest = daily_data.tail(1)
                    print(f"最新数据: {latest[['date', 'close']].to_string(index=False)}")
            
            # 3. 获取实时数据
            print(f"\n3. 获取实时数据...")
            realtime_data = manager.get_realtime_data(test_symbols[:2])
            
            if realtime_data is not None and not realtime_data.empty:
                print(f"成功获取 {len(realtime_data)} 条实时数据")
                print(f"数据列: {list(realtime_data.columns)}")
            
            # 4. 获取市场概况
            print(f"\n4. 获取市场概况...")
            market_data = manager.get_market_summary()
            
            if market_data is not None and not market_data.empty:
                print("成功获取市场概况")
                print(f"数据列: {list(market_data.columns)}")
            
            # 5. 测试缓存性能
            print(f"\n5. 测试缓存性能...")
            start_time = datetime.now()
            data1 = manager.get_stock_list()
            first_time = (datetime.now() - start_time).total_seconds()
            
            start_time = datetime.now()
            data2 = manager.get_stock_list()
            second_time = (datetime.now() - start_time).total_seconds()
            
            print(f"第一次耗时: {first_time:.3f}秒")
            print(f"第二次耗时: {second_time:.3f}秒")
            print(f"性能提升: {first_time/second_time:.1f}倍")
            
            # 6. 健康检查
            print(f"\n6. 系统健康检查...")
            health = manager.health_check()
            print(f"系统状态: {health['status']}")
            
            # 7. 统计信息
            print(f"\n7. 统计信息...")
            stats = manager.get_stats()
            manager_stats = stats['manager_stats']
            print(f"总请求数: {manager_stats['total_requests']}")
            print(f"成功请求: {manager_stats['successful_requests']}")
            print(f"失败请求: {manager_stats['failed_requests']}")
            
            if 'akshare' in stats['provider_stats']:
                provider_stats = stats['provider_stats']['akshare']
                print(f"缓存命中率: {provider_stats['cache_hit_rate']:.1%}")
            
            print("\n" + "="*60)
            print("数据获取功能演示完成！")
            print("所有核心功能都正常工作")
            print("="*60)
        
        else:
            print("获取股票列表失败")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"演示失败: {e}")


if __name__ == "__main__":
    main()
